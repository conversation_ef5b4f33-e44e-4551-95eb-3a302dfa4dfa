<?php $__env->startSection('title', '<PERSON> Kelas'); ?>

<?php $__env->startSection('content'); ?>
    <?php echo $__env->make('admin.components.page-title', [
        'title' => 'Edit Kelas',
        'breadcrumb' => 'Manajemen <PERSON>',
    ], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

    <div class="row">
        <div class="col-lg-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Form Edit Data Kelas</h5>
                </div>
                <div class="card-body">
                    <form id="editClassroomForm" action="<?php echo e(route('admin.classrooms.update', $classroom->id)); ?>" method="POST">
                        <?php echo csrf_field(); ?>
                        <?php echo method_field('PUT'); ?>

                        <div class="row g-3">
                            <!-- Tab Navigation -->
                            <div class="col-12">
                                <ul class="nav nav-pills nav-justified nav-primary mb-3" role="tablist">
                                    <li class="nav-item">
                                        <a class="nav-link active" data-bs-toggle="tab" href="#basic-info" role="tab">
                                            Informasi Dasar
                                        </a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link" data-bs-toggle="tab" href="#additional-info" role="tab">
                                            Informasi Tambahan
                                        </a>
                                    </li>
                                </ul>
                            </div>

                            <!-- Tab Content -->
                            <div class="col-12">
                                <div class="tab-content">
                                    <!-- Basic Information -->
                                    <div class="tab-pane active" id="basic-info" role="tabpanel">
                                        <div class="row g-3">
                                            <!-- Name -->
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label for="name" class="form-label">Nama Kelas <span class="text-danger">*</span></label>
                                                    <input type="text" class="form-control" id="name" name="name"
                                                           placeholder="Masukkan nama kelas" value="<?php echo e($classroom->name); ?>" required>
                                                    <div class="invalid-feedback" data-field="name"></div>
                                                </div>
                                            </div>

                                            <!-- Level -->
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label for="level" class="form-label">Level <span class="text-danger">*</span></label>
                                                    <select class="form-select" data-choices id="level" name="level" required>
                                                        <option value="">Pilih Level</option>
                                                        <?php $__currentLoopData = $levels; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $value => $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                            <option value="<?php echo e($value); ?>" <?php echo e($classroom->level->value == $value ? 'selected' : ''); ?>>
                                                                <?php echo e($label); ?>

                                                            </option>
                                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                    </select>
                                                    <div class="invalid-feedback" data-field="level"></div>
                                                </div>
                                            </div>

                                            <!-- Program -->
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label for="program_id" class="form-label">Program <span class="text-danger">*</span></label>
                                                    <select class="form-select" data-choices id="program_id" name="program_id" required>
                                                        <option value="">Pilih Program</option>
                                                        <?php $__currentLoopData = $programs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $program): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                            <option value="<?php echo e($program->id); ?>" <?php echo e($classroom->program_id == $program->id ? 'selected' : ''); ?>><?php echo e($program->name); ?></option>
                                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                    </select>
                                                    <div class="invalid-feedback" data-field="program_id"></div>
                                                </div>
                                            </div>

                                            <!-- Teacher -->
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label for="teacher_id" class="form-label">Wali Kelas <span class="text-danger">*</span></label>
                                                    <select class="form-select" data-choices id="teacher_id" name="teacher_id" required>
                                                        <option value="">Pilih Wali Kelas</option>
                                                        <?php $__currentLoopData = $teachers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $teacher): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                            <option value="<?php echo e($teacher->id); ?>" <?php echo e($classroom->teacher_id == $teacher->id ? 'selected' : ''); ?>><?php echo e($teacher->user->name); ?></option>
                                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                    </select>
                                                    <div class="invalid-feedback" data-field="teacher_id"></div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Additional Information -->
                                    <div class="tab-pane" id="additional-info" role="tabpanel">
                                        <div class="row g-3">
                                            <!-- Academic Year -->
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label for="academic_year_id" class="form-label">Tahun Akademik <span class="text-danger">*</span></label>
                                                    <select class="form-select" data-choices id="academic_year_id" name="academic_year_id" required>
                                                        <option value="">Pilih Tahun Akademik</option>
                                                        <?php $__currentLoopData = $academicYears; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $academicYear): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                            <option value="<?php echo e($academicYear->id); ?>" <?php echo e($classroom->academic_year_id == $academicYear->id ? 'selected' : ''); ?>>
                                                                <?php echo e($academicYear->formatted_name); ?>

                                                            </option>
                                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                    </select>
                                                    <div class="invalid-feedback" data-field="academic_year_id"></div>
                                                </div>
                                            </div>

                                            <!-- Capacity -->
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label for="capacity" class="form-label">Kapasitas <span class="text-danger">*</span></label>
                                                    <input type="number" class="form-control" id="capacity" name="capacity"
                                                           placeholder="Masukkan kapasitas kelas" min="1" value="<?php echo e($classroom->capacity); ?>" required>
                                                    <div class="invalid-feedback" data-field="capacity"></div>
                                                </div>
                                            </div>

                                            <!-- Shift -->
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label for="shift_id" class="form-label">Shift</label>
                                                    <select class="form-select" data-choices id="shift_id" name="shift_id">
                                                        <option value="">Pilih Shift (Opsional)</option>
                                                        <?php $__currentLoopData = $shifts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $shift): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                            <option value="<?php echo e($shift->id); ?>" <?php echo e($classroom->shift_id == $shift->id ? 'selected' : ''); ?>>
                                                                <?php echo e($shift->name); ?>

                                                            </option>
                                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                    </select>
                                                    <div class="invalid-feedback" data-field="shift_id"></div>
                                                    <div class="form-text">Shift menentukan jadwal jam pelajaran yang akan digunakan.</div>
                                                </div>
                                            </div>

                                            <!-- Status -->
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label for="status" class="form-label">Status <span class="text-danger">*</span></label>
                                                    <select class="form-select" data-choices id="status" name="status" required>
                                                        <option value="">Pilih Status</option>
                                                        <?php $__currentLoopData = $statuses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $value => $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                            <option value="<?php echo e($value); ?>" <?php echo e($classroom->status?->value === $value ? 'selected' : ''); ?>>
                                                                <?php echo e($label); ?>

                                                            </option>
                                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                    </select>
                                                    <div class="invalid-feedback" data-field="status"></div>
                                                </div>
                                            </div>

                                            <!-- Description -->
                                            <div class="col-12">
                                                <div class="form-group">
                                                    <label for="description" class="form-label">Deskripsi</label>
                                                    <textarea class="form-control" id="description" name="description"
                                                              rows="3" placeholder="Masukkan deskripsi kelas (opsional)"><?php echo e($classroom->description); ?></textarea>
                                                    <div class="invalid-feedback" data-field="description"></div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Buttons -->
                            <div class="col-12 text-end">
                                <a href="<?php echo e(route('admin.classrooms.index')); ?>" class="btn btn-light">Batal</a>
                                <button type="submit" class="btn btn-primary" id="submit-btn">
                                    <i class="ri-save-line align-bottom me-1"></i> Simpan Perubahan
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.partials.plugins.jquery', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
<?php echo $__env->make('admin.partials.plugins.sweetalert', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

<?php $__env->startPush('scripts'); ?>
    <?php echo $__env->make('admin.pages.classroom.scripts.edit-form-script', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('admin.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\rawooh-v2\resources\views/admin/pages/classroom/edit.blade.php ENDPATH**/ ?>