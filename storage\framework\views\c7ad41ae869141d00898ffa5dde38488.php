<?php $__env->startSection('title', 'Manajemen Kehadiran'); ?>

<?php $__env->startSection('content'); ?>
    <?php echo $__env->make('admin.components.page-title', [
        'title' => 'Manajemen Kehadiran',
        'breadcrumb' => 'Kehadiran',
    ], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

    <div class="row">
        <div class="col-lg-12">
            <div class="card">
                <div class="card-header border-bottom-dashed">
                    <div class="d-flex align-items-center">
                        <h5 class="card-title mb-0 flex-grow-1">
                            <i class="ri-calendar-check-line text-primary me-1"></i> Daftar Kehadiran
                        </h5>
                        <div class="flex-shrink-0">
                            <a href="<?php echo e(route('admin.attendances.create')); ?>" class="btn btn-success">
                                <i class="ri-add-line align-bottom"></i> <PERSON><PERSON> Kehadiran
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Filter Section -->
                <div class="card-header border-bottom-dashed">
                    <div class="row g-3">
                        <div class="col-md-3">
                            <div class="search-box">
                                <input type="text" id="search-input" class="form-control search" placeholder="Cari...">
                                <i class="ri-search-line search-icon"></i>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <select id="filter-classroom" class="form-select" data-choices data-choices-search-false>
                                <option value="">Pilih Kelas</option>
                                <?php $__currentLoopData = $classrooms; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $classroom): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($classroom->id); ?>"><?php echo e($classroom->name); ?></option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <select id="filter-status" class="form-select" data-choices data-choices-search-false>
                                <option value="">Semua Status</option>
                                <?php $__currentLoopData = $attendanceStatuses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $value => $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($value); ?>"><?php echo e($label); ?></option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <select id="filter-type" class="form-select" data-choices data-choices-search-false>
                                <option value="">Semua Tipe</option>
                                <?php $__currentLoopData = $attendantTypes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $value => $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($value); ?>"><?php echo e($label); ?></option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <button type="button" id="reset-filter" class="btn btn-light w-100">
                                <i class="ri-refresh-line align-bottom"></i> Reset
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Date Filter Section -->
                <div class="card-header border-bottom-dashed">
                    <div class="row g-3">
                        <div class="col-md-3">
                            <div class="input-group">
                                <span class="input-group-text">Dari</span>
                                <input type="date" id="filter-date-from" class="form-control">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="input-group">
                                <span class="input-group-text">Sampai</span>
                                <input type="date" id="filter-date-to" class="form-control">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <a href="<?php echo e(route('admin.attendances.summary')); ?>" class="btn btn-info w-100">
                                <i class="ri-bar-chart-line align-bottom"></i> Lihat Ringkasan
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Table Section -->
                <div class="card-body">
                    <div class="table-responsive">
                        <table id="datatable" class="table nowrap align-middle table-striped" style="width:100%">
                            <thead class="table-light">
                                <tr>
                                    <th>No</th>
                                    <th>Tanggal</th>
                                    <th>Kelas</th>
                                    <th>Mata Pelajaran</th>
                                    <th>Nama</th>
                                    <th>Tipe</th>
                                    <th>Status</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- Data akan diisi via AJAX -->
                            </tbody>
                        </table>
                    </div>
                </div>
                <!-- End Table Section -->
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
    <!-- DataTables CSS -->
    <link href="<?php echo e(asset('assets/libs/datatables.net-bs5/css/dataTables.bootstrap5.min.css')); ?>" rel="stylesheet" type="text/css" />
    <style>
        .dataTables_filter {
            display: none;
        }

        .dataTables_length {
            padding-bottom: 15px;
        }
    </style>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('admin.partials.plugins.jquery', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
<?php echo $__env->make('admin.partials.plugins.datatables', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
<?php echo $__env->make('admin.partials.plugins.sweetalert', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

<?php $__env->startPush('scripts'); ?>
    <script type="text/javascript">
        let table;
        $(document).ready(function() {
            // Initialize DataTable with enhanced configuration
            table = $('#datatable').DataTable({
                processing: true,
                serverSide: true,
                ajax: {
                    url: "<?php echo e(route('admin.attendances.index')); ?>",
                    data: function(d) {
                        return {
                            ...d,
                            classroom_id: $('#filter-classroom').val(),
                            attendance_status: $('#filter-status').val(),
                            attendant_type: $('#filter-type').val(),
                            date_from: $('#filter-date-from').val(),
                            date_to: $('#filter-date-to').val(),
                            search: $('#search-input').val()
                        };
                    }
                },
                columns: [{
                        data: 'DT_RowIndex',
                        name: 'DT_RowIndex',
                        orderable: false,
                        searchable: false
                    },
                    {
                        data: 'attendance_date',
                        name: 'attendance_date'
                    },
                    {
                        data: 'class_name',
                        name: 'class_name'
                    },
                    {
                        data: 'subject_name',
                        name: 'subject_name'
                    },
                    {
                        data: null,
                        render: function(data) {
                            if (data.attendant_type === 'student') {
                                return data.student_name;
                            } else {
                                return data.teacher_name;
                            }
                        }
                    },
                    {
                        data: 'attendant_type',
                        name: 'attendant_type'
                    },
                    {
                        data: 'attendance_status',
                        name: 'attendance_status'
                    },
                    {
                        data: 'action',
                        name: 'action',
                        orderable: false,
                        searchable: false
                    }
                ],
                order: [
                    [1, 'desc']
                ],
                language: {
                    url: "<?php echo e(asset('assets/json/datatables-id.json')); ?>"
                },
                drawCallback: function() {
                    // Initialize tooltips
                    $('[data-bs-toggle="tooltip"]').tooltip();
                }
            });

            // Apply filters when changed
            $('#filter-classroom, #filter-status, #filter-type, #filter-date-from, #filter-date-to').change(function() {
                table.ajax.reload();
            });

            // Apply search when typing
            $('#search-input').keyup(function() {
                table.ajax.reload();
            });

            // Reset filters
            $('#reset-filter').click(function() {
                $('#filter-classroom').val('').trigger('change');
                $('#filter-status').val('').trigger('change');
                $('#filter-type').val('').trigger('change');
                $('#filter-date-from').val('');
                $('#filter-date-to').val('');
                $('#search-input').val('');
                table.ajax.reload();
            });

            // Delete confirmation
            $(document).on('click', '.delete-btn', function(e) {
                e.preventDefault();
                const deleteUrl = $(this).data('url');

                Swal.fire({
                    title: 'Apakah Anda yakin?',
                    text: "Data kehadiran akan dihapus permanen!",
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonText: 'Ya, hapus!',
                    cancelButtonText: 'Batal',
                    confirmButtonClass: 'btn btn-danger mt-2',
                    cancelButtonClass: 'btn btn-secondary ms-2 mt-2',
                    buttonsStyling: false
                }).then(function(result) {
                    if (result.isConfirmed) {
                        $.ajax({
                            url: deleteUrl,
                            type: 'POST',
                            data: {
                                _method: 'DELETE',
                                _token: '<?php echo e(csrf_token()); ?>'
                            },
                            success: function(response) {
                                Swal.fire({
                                    title: 'Berhasil!',
                                    text: 'Data kehadiran berhasil dihapus.',
                                    icon: 'success',
                                    confirmButtonClass: 'btn btn-primary mt-2',
                                    buttonsStyling: false
                                });
                                table.ajax.reload();
                            },
                            error: function(xhr) {
                                Swal.fire({
                                    title: 'Error!',
                                    text: xhr.responseJSON.message || 'Terjadi kesalahan saat menghapus data.',
                                    icon: 'error',
                                    confirmButtonClass: 'btn btn-primary mt-2',
                                    buttonsStyling: false
                                });
                            }
                        });
                    }
                });
            });
        });
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('admin.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\rawooh-v2\resources\views/admin/pages/attendance/index.blade.php ENDPATH**/ ?>