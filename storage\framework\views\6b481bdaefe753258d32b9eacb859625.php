<?php $__env->startSection('title', 'Detail Siswa'); ?>

<?php $__env->startSection('content'); ?>
    <?php echo $__env->make('admin.components.page-title', [
        'title' => 'Detail Siswa',
        'breadcrumb' => 'Manajemen Akun',
    ], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

    <div class="row">
        <div class="col-xl-4">
            <!-- Student Information Card -->
            <div class="card">
                <div class="card-header border-bottom-dashed">
                    <div class="d-flex align-items-center">
                        <h5 class="card-title mb-0 flex-grow-1">Profil Siswa</h5>
                        <div class="flex-shrink-0">
                            <a href="<?php echo e(route('admin.students.edit', $student->id)); ?>" class="btn btn-soft-primary">
                                <i class="ri-edit-line"></i> Edit
                            </a>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="text-center">
                        <?php if($student->profile_picture): ?>
                            <img src="<?php echo e(asset('storage/' . $student->profile_picture)); ?>" alt="Profile Picture" class="avatar-xl rounded-circle img-thumbnail shadow-lg mb-4">
                        <?php else: ?>
                            <div class="avatar-xl rounded-circle bg-light d-inline-block mb-4">
                                <span class="avatar-title text-primary display-5 rounded-circle shadow-sm">
                                    <?php echo e(strtoupper(substr($student->user->name, 0, 1))); ?>

                                </span>
                            </div>
                        <?php endif; ?>

                        <div class="mb-4">
                            <h5 class="mb-1"><?php echo e($student->user->name); ?></h5>
                            <div class="badge badge-outline-<?php echo e($student->user->status == 'active' ? 'success' : 'danger'); ?> fs-12">
                                <?php echo e($student->user->status == 'active' ? 'Aktif' : 'Nonaktif'); ?>

                            </div>
                        </div>
                    </div>

                    <div class="table-responsive">
                        <table class="table table-borderless mb-0">
                            <tbody>
                                <tr>
                                    <th class="ps-0" scope="row"><i class="ri-user-line text-muted me-2"></i> NIS</th>
                                    <td class="text-muted"><?php echo e($student->nis); ?></td>
                                </tr>
                                <tr>
                                    <th class="ps-0" scope="row"><i class="ri-number-2 text-muted me-2"></i> NISN</th>
                                    <td class="text-muted"><?php echo e($student->nisn); ?></td>
                                </tr>
                                <tr>
                                    <th class="ps-0" scope="row"><i class="ri-mail-line text-muted me-2"></i> Email</th>
                                    <td class="text-muted"><?php echo e($student->user->email); ?></td>
                                </tr>
                                <tr>
                                    <th class="ps-0" scope="row"><i class="ri-phone-line text-muted me-2"></i> Telepon</th>
                                    <td class="text-muted"><?php echo e($student->phone ?: '-'); ?></td>
                                </tr>
                                <tr>
                                    <th class="ps-0" scope="row"><i class="ri-calendar-line text-muted me-2"></i> Tahun Masuk</th>
                                    <td class="text-muted"><?php echo e($student->entry_year); ?></td>
                                </tr>
                                <tr>
                                    <th class="ps-0" scope="row"><i class="ri-user-3-line text-muted me-2"></i> Wali</th>
                                    <td class="text-muted"><?php echo e($student->parent_name); ?></td>
                                </tr>
                                <tr>
                                    <th class="ps-0" scope="row"><i class="ri-phone-line text-muted me-2"></i> Telepon Wali</th>
                                    <td class="text-muted"><?php echo e($student->parent_phone); ?></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Current Classroom Card -->
            <div class="card">
                <div class="card-header border-bottom-dashed">
                    <h5 class="card-title mb-0">Kelas Saat Ini</h5>
                </div>
                <div class="card-body">
                    <?php if($student->currentClassroom): ?>
                        <div class="d-flex align-items-center mb-3">
                            <div class="flex-shrink-0">
                                <div class="avatar-sm">
                                    <div class="avatar-title bg-soft-primary text-primary rounded fs-18">
                                        <i class="ri-building-line"></i>
                                    </div>
                                </div>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h6 class="mb-1"><?php echo e($student->currentClassroom->name); ?></h6>
                                <p class="text-muted mb-0"><?php echo e($student->currentClassroom->academicYear->name); ?> (<?php echo e($student->currentClassroom->academicYear->year_start); ?>/<?php echo e($student->currentClassroom->academicYear->year_end); ?>)</p>
                            </div>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-3">
                            <div class="avatar-sm mx-auto mb-3">
                                <div class="avatar-title bg-soft-warning text-warning rounded-circle fs-24">
                                    <i class="ri-information-line"></i>
                                </div>
                            </div>
                            <h6>Belum Terdaftar di Kelas</h6>
                            <p class="text-muted mb-0">Siswa belum terdaftar di kelas manapun.</p>

                            <?php if($student->user->status == 'active'): ?>
                                <div class="mt-4">
                                    <button type="button" class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#enrollModal">
                                        <i class="ri-user-add-line align-bottom me-1"></i> Daftarkan ke Kelas
                                    </button>
                                </div>
                            <?php endif; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <div class="col-xl-8">
            <div class="card">
                <div class="card-header border-bottom-dashed">
                    <!-- Nav tabs -->
                    <ul class="nav nav-tabs-custom rounded card-header-tabs border-bottom-0 gap-2" role="tablist">
                        <li class="nav-item">
                            <a class="nav-link active" data-bs-toggle="tab" href="#personal-info-tab" role="tab">
                                <i class="ri-user-line me-1"></i> Informasi Pribadi
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" data-bs-toggle="tab" href="#parent-info-tab" role="tab">
                                <i class="ri-team-line me-1"></i> Informasi Orang Tua
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" data-bs-toggle="tab" href="#academic-info-tab" role="tab">
                                <i class="ri-book-2-line me-1"></i> Informasi Akademik
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" data-bs-toggle="tab" href="#account-settings-tab" role="tab">
                                <i class="ri-user-settings-line me-1"></i> Pengaturan Akun
                            </a>
                        </li>
                    </ul>
                </div>

                <div class="card-body">
                    <!-- Tab panes -->
                    <div class="tab-content">
                        <!-- Personal Information Tab -->
                        <div class="tab-pane active" id="personal-info-tab" role="tabpanel">
                            <div class="table-responsive">
                                <table class="table table-borderless mb-0">
                                    <tbody>
                                        <tr>
                                            <th class="ps-0" scope="row" width="25%">Nama Lengkap</th>
                                            <td class="text-muted"><?php echo e($student->user->name); ?></td>
                                        </tr>
                                        <tr>
                                            <th class="ps-0" scope="row">Tempat, Tanggal Lahir</th>
                                            <td class="text-muted"><?php echo e($student->birth_place); ?>, <?php echo e($student->birth_date->format('d F Y')); ?></td>
                                        </tr>
                                        <tr>
                                            <th class="ps-0" scope="row">Jenis Kelamin</th>
                                            <td class="text-muted"><?php echo e($student->gender == 'male' ? 'Laki-laki' : 'Perempuan'); ?></td>
                                        </tr>
                                        <tr>
                                            <th class="ps-0" scope="row">Agama</th>
                                            <td class="text-muted"><?php echo e(\App\Enums\ReligionEnum::getLabel($student->religion)); ?></td>
                                        </tr>
                                        <tr>
                                            <th class="ps-0" scope="row">Alamat</th>
                                            <td class="text-muted"><?php echo e($student->address); ?></td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- Parent Information Tab -->
                        <div class="tab-pane" id="parent-info-tab" role="tabpanel">
                            <div class="table-responsive">
                                <table class="table table-borderless mb-0">
                                    <tbody>
                                        <tr>
                                            <th class="ps-0" scope="row" width="25%">Nama Orang Tua/Wali</th>
                                            <td class="text-muted"><?php echo e($student->parent_name); ?></td>
                                        </tr>
                                        <tr>
                                            <th class="ps-0" scope="row">Nomor Telepon</th>
                                            <td class="text-muted"><?php echo e($student->parent_phone); ?></td>
                                        </tr>
                                        <tr>
                                            <th class="ps-0" scope="row">Pekerjaan</th>
                                            <td class="text-muted"><?php echo e($student->parent_occupation ?: '-'); ?></td>
                                        </tr>
                                        <tr>
                                            <th class="ps-0" scope="row">Alamat</th>
                                            <td class="text-muted"><?php echo e($student->parent_address ?: '(Sama dengan alamat siswa)'); ?></td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- Academic Information Tab -->
                        <div class="tab-pane" id="academic-info-tab" role="tabpanel">
                            <!-- Classroom History Table -->
                            <div class="card border-0 shadow-none mb-0">
                                <div class="card-header border-bottom-dashed align-items-center d-flex">
                                    <h4 class="card-title mb-0 flex-grow-1">Riwayat Kelas</h4>
                                    <?php if($student->user->status == 'active'): ?>
                                        <div class="flex-shrink-0">
                                            <button type="button" class="btn btn-soft-primary btn-sm" data-bs-toggle="modal" data-bs-target="#enrollModal">
                                                <i class="ri-add-line align-bottom"></i> Tambah Kelas
                                            </button>
                                        </div>
                                    <?php endif; ?>
                                </div>
                                <div class="card-body pt-0">
                                    <?php if($classrooms->count() > 0): ?>
                                        <table class="table table-nowrap align-middle mb-0">
                                            <thead class="table-light">
                                                <tr>
                                                    <th scope="col">Kelas</th>
                                                    <th scope="col">Tahun Akademik</th>
                                                    <th scope="col">Periode</th>
                                                    <th scope="col">Status</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php $__currentLoopData = $classrooms; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $classroom): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <tr>
                                                        <td><?php echo e($classroom->name); ?></td>
                                                        <td><?php echo e($classroom->academicYear->formatted_name); ?></td>
                                                        <td><?php echo e($classroom->academicYear->formatted_period); ?></td>
                                                        <td>
                                                            <?php if($student->currentClassroom && $student->currentClassroom->id == $classroom->id): ?>
                                                                <span class="badge badge-soft-success">Aktif</span>
                                                            <?php else: ?>
                                                                <span class="badge badge-soft-secondary">Selesai</span>
                                                            <?php endif; ?>
                                                        </td>
                                                    </tr>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </tbody>
                                        </table>
                                    <?php else: ?>
                                        <div class="text-center p-4">
                                            <div class="avatar-md mx-auto mb-4">
                                                <div class="avatar-title bg-light rounded-circle text-primary display-6">
                                                    <i class="ri-file-text-line"></i>
                                                </div>
                                            </div>
                                            <h5 class="mb-2">Belum Ada Data</h5>
                                            <p class="text-muted mb-0">Siswa belum memiliki riwayat kelas.</p>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>

                        <!-- Account Settings Tab -->
                        <div class="tab-pane" id="account-settings-tab" role="tabpanel">
                            <div class="row">
                                <div class="col-lg-12">
                                    <div class="card border-0 shadow-none mb-0">
                                        <div class="card-header border-bottom-dashed align-items-center d-flex">
                                            <h4 class="card-title mb-0 flex-grow-1">Pengaturan Akun</h4>
                                            <div class="flex-shrink-0">
                                                <a href="<?php echo e(route('admin.students.edit', $student->id)); ?>" class="btn btn-soft-primary btn-sm">
                                                    <i class="ri-edit-line align-bottom"></i> Edit Akun
                                                </a>
                                            </div>
                                        </div>
                                        <div class="card-body">
                                            <div class="table-responsive">
                                                <table class="table table-borderless mb-0">
                                                    <tbody>
                                                        <tr>
                                                            <th class="ps-0" scope="row" width="25%">Nama Lengkap</th>
                                                            <td class="text-muted"><?php echo e($student->user->name); ?></td>
                                                        </tr>
                                                        <tr>
                                                            <th class="ps-0" scope="row">Email</th>
                                                            <td class="text-muted"><?php echo e($student->user->email); ?></td>
                                                        </tr>
                                                        <tr>
                                                            <th class="ps-0" scope="row">Status</th>
                                                            <td>
                                                                <span class="badge <?php echo e($student->user->status == 'active' ? 'bg-success' : 'bg-danger'); ?>">
                                                                    <?php echo e($student->user->status == 'active' ? 'Aktif' : 'Nonaktif'); ?>

                                                                </span>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <th class="ps-0" scope="row">Peran</th>
                                                            <td class="text-muted">Siswa</td>
                                                        </tr>
                                                        <tr>
                                                            <th class="ps-0" scope="row">Terakhir Diperbarui</th>
                                                            <td class="text-muted"><?php echo e($student->user->updated_at->format('d F Y H:i')); ?></td>
                                                        </tr>
                                                    </tbody>
                                                </table>
                                            </div>

                                            <div class="mt-4">
                                                <h5 class="mb-3">Tindakan Akun</h5>
                                                <div class="d-flex gap-2">
                                                    <button type="button" class="btn btn-soft-<?php echo e($student->user->status == 'active' ? 'danger' : 'success'); ?> btn-sm"
                                                            onclick="changeStatus(<?php echo e($student->id); ?>, <?php echo e($student->user->status == 'active' ? 'false' : 'true'); ?>)">
                                                        <i class="ri-<?php echo e($student->user->status == 'active' ? 'user-unfollow-line' : 'user-follow-line'); ?> align-bottom me-1"></i>
                                                        <?php echo e($student->user->status == 'active' ? 'Nonaktifkan Akun' : 'Aktifkan Akun'); ?>

                                                    </button>
                                                    <button type="button" class="btn btn-soft-danger btn-sm" onclick="deleteStudent(<?php echo e($student->id); ?>)">
                                                        <i class="ri-delete-bin-line align-bottom me-1"></i> Hapus Akun
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Enroll Modal -->
    <div class="modal fade" id="enrollModal" tabindex="-1" aria-labelledby="enrollModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-light p-3">
                    <h5 class="modal-title" id="enrollModalLabel">Daftarkan Siswa ke Kelas</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form id="enrollForm" action="<?php echo e(route('admin.students.enroll', $student->id)); ?>" method="POST">
                    <?php echo csrf_field(); ?>
                    <div class="modal-body">
                        <div id="enroll-error-msg" class="alert alert-danger py-2 d-none"></div>

                        <div class="mb-3">
                            <label for="academic_year_id" class="form-label">Tahun Akademik <span class="text-danger">*</span></label>
                            <select class="form-select" data-choices id="academic_year_id" name="academic_year_id" required>
                                <option value="">Pilih Tahun Akademik</option>
                                <?php $__currentLoopData = $academicYears; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $academicYear): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($academicYear->id); ?>"><?php echo e($academicYear->formatted_name); ?> (<?php echo e($academicYear->formatted_period); ?>)</option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                            <div class="invalid-feedback">Silakan pilih tahun akademik.</div>
                        </div>

                        <div class="mb-3">
                            <label for="classroom_id" class="form-label">Kelas <span class="text-danger">*</span></label>
                            <select class="form-select" data-choices id="classroom_id" name="classroom_id" required disabled>
                                <option value="">Pilih kelas</option>
                            </select>
                            <div class="invalid-feedback">Silakan pilih kelas.</div>
                            <div id="classroom-loading" class="d-none">
                                <div class="spinner-border spinner-border-sm text-primary mt-2" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                                <span class="ms-1">Memuat kelas...</span>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-ghost-secondary" data-bs-dismiss="modal">Batal</button>
                        <button type="button" class="btn btn-primary" id="enrollBtn">Daftarkan</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.partials.plugins.jquery', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
<?php echo $__env->make('admin.partials.plugins.sweetalert', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

<?php $__env->startPush('scripts'); ?>
    <script type="text/javascript">
        $(document).ready(function() {
            // Handle academic year change to load classrooms
            $('#academic_year_id').on('change', function() {
                const academicYearId = $(this).val();
                const classroomSelect = $('#classroom_id');
                const loadingElement = $('#classroom-loading');

                // Reset classroom select
                classroomSelect.html('<option value="">Pilih kelas</option>');

                if (!academicYearId) {
                    classroomSelect.prop('disabled', true);
                    return;
                }

                // Show loading indicator
                loadingElement.removeClass('d-none');
                classroomSelect.prop('disabled', true);

                // Fetch classrooms by academic year
                $.ajax({
                    url: "<?php echo e(route('admin.ajax.classrooms.by.academic.year')); ?>",
                    type: 'GET',
                    data: {
                        academic_year_id: academicYearId
                    },
                    dataType: 'json',
                    success: function(response) {
                        if (response.success) {
                            // Populate classroom options
                            $.each(response.data, function(index, classroom) {
                                classroomSelect.append('<option value="' + classroom.id + '">' + classroom.name + '</option>');
                            });

                            // Enable select
                            classroomSelect.prop('disabled', false);
                        } else {
                            // Show error message
                            Swal.fire({
                                title: 'Error',
                                text: response.message || 'Terjadi kesalahan, silakan coba lagi.',
                                icon: 'error'
                            });
                        }
                    },
                    error: function(xhr) {
                        // Show error message
                        Swal.fire({
                            title: 'Error',
                            text: xhr.responseJSON?.message || 'Terjadi kesalahan, silakan coba lagi.',
                            icon: 'error'
                        });
                    },
                    complete: function() {
                        // Hide loading indicator
                        loadingElement.addClass('d-none');
                    }
                });
            });

            // Handle enroll button click
            $('#enrollBtn').on('click', function() {
                const form = document.getElementById('enrollForm');
                const errorMsg = document.getElementById('enroll-error-msg');
                const button = this;

                // Reset error message
                errorMsg.classList.add('d-none');

                // Validate form
                if (!form.checkValidity()) {
                    form.reportValidity();
                    return;
                }

                // Show loading state
                const originalBtnContent = button.innerHTML;
                button.disabled = true;
                button.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Memproses...';

                // Submit form via AJAX
                $.ajax({
                    url: form.action,
                    method: 'POST',
                    data: $(form).serialize(),
                    dataType: 'json',
                    success: function(response) {
                        if (response.success) {
                            // Show success notification
                            Swal.fire({
                                title: 'Berhasil',
                                text: response.message,
                                icon: 'success',
                                showConfirmButton: false,
                                timer: 1500
                            }).then(() => {
                                // Reload page
                                window.location.reload();
                            });
                        } else {
                            // Show error message
                            errorMsg.textContent = response.message;
                            errorMsg.classList.remove('d-none');
                        }
                    },
                    error: function(xhr) {
                        // Show error message
                        let errorMessage = 'Terjadi kesalahan, silakan coba lagi.';

                        if (xhr.status === 422) {
                            // Validation errors
                            const errors = xhr.responseJSON.errors;
                            const firstError = Object.values(errors)[0][0];
                            if (firstError) {
                                errorMessage = firstError;
                            }
                        } else if (xhr.responseJSON && xhr.responseJSON.message) {
                            errorMessage = xhr.responseJSON.message;
                        }

                        errorMsg.textContent = errorMessage;
                        errorMsg.classList.remove('d-none');
                    },
                    complete: function() {
                        // Restore button state
                        button.disabled = false;
                        button.innerHTML = originalBtnContent;
                    }
                });
            });
        });

        // Function to change student account status
        function changeStatus(studentId, status) {
            Swal.fire({
                title: status ? 'Aktifkan Akun' : 'Nonaktifkan Akun',
                text: status ? 'Apakah Anda yakin ingin mengaktifkan akun siswa ini?' : 'Apakah Anda yakin ingin menonaktifkan akun siswa ini?',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonText: 'Ya, ' + (status ? 'Aktifkan' : 'Nonaktifkan'),
                cancelButtonText: 'Batal',
                reverseButtons: true
            }).then((result) => {
                if (result.isConfirmed) {
                    // Show loading
                    Swal.fire({
                        title: 'Memproses...',
                        text: 'Sedang mengubah status akun',
                        allowOutsideClick: false,
                        allowEscapeKey: false,
                        didOpen: () => {
                            Swal.showLoading();
                        }
                    });

                    // Send AJAX request to change status
                    $.ajax({
                        url: "<?php echo e(route('admin.students.change-status', ':id')); ?>".replace(':id', studentId),
                        type: 'POST',
                        data: {
                            _token: "<?php echo e(csrf_token()); ?>",
                            status: status
                        },
                        success: function(response) {
                            if (response.success) {
                                Swal.fire({
                                    title: 'Berhasil!',
                                    text: response.message,
                                    icon: 'success',
                                    showConfirmButton: false,
                                    timer: 1500
                                }).then(() => {
                                    // Reload page
                                    window.location.reload();
                                });
                            } else {
                                Swal.fire({
                                    title: 'Gagal',
                                    text: response.message || 'Terjadi kesalahan saat mengubah status akun',
                                    icon: 'error'
                                });
                            }
                        },
                        error: function(xhr) {
                            Swal.fire({
                                title: 'Gagal',
                                text: xhr.responseJSON?.message || 'Terjadi kesalahan saat mengubah status akun',
                                icon: 'error'
                            });
                        }
                    });
                }
            });
        }

        // Function to delete student account
        function deleteStudent(studentId) {
            Swal.fire({
                title: 'Hapus Akun',
                text: 'Apakah Anda yakin ingin menghapus akun siswa ini? Tindakan ini tidak dapat dibatalkan!',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonText: 'Ya, Hapus',
                cancelButtonText: 'Batal',
                reverseButtons: true
            }).then((result) => {
                if (result.isConfirmed) {
                    // Show loading
                    Swal.fire({
                        title: 'Memproses...',
                        text: 'Sedang menghapus akun',
                        allowOutsideClick: false,
                        allowEscapeKey: false,
                        didOpen: () => {
                            Swal.showLoading();
                        }
                    });

                    // Send AJAX request to delete student
                    $.ajax({
                        url: "<?php echo e(route('admin.students.destroy', ':id')); ?>".replace(':id', studentId),
                        type: 'DELETE',
                        data: {
                            _token: "<?php echo e(csrf_token()); ?>"
                        },
                        success: function(response) {
                            if (response.success) {
                                Swal.fire({
                                    title: 'Berhasil!',
                                    text: response.message,
                                    icon: 'success',
                                    showConfirmButton: false,
                                    timer: 1500
                                }).then(() => {
                                    // Redirect to student index
                                    window.location.href = "<?php echo e(route('admin.students.index')); ?>";
                                });
                            } else {
                                Swal.fire({
                                    title: 'Gagal',
                                    text: response.message || 'Terjadi kesalahan saat menghapus akun',
                                    icon: 'error'
                                });
                            }
                        },
                        error: function(xhr) {
                            Swal.fire({
                                title: 'Gagal',
                                text: xhr.responseJSON?.message || 'Terjadi kesalahan saat menghapus akun',
                                icon: 'error'
                            });
                        }
                    });
                }
            });
        }
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('admin.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\rawooh-v2\resources\views/admin/pages/student/show.blade.php ENDPATH**/ ?>