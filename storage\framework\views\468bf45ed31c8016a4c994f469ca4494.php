<script type="text/javascript">
    $(document).ready(function() {
        // Form submission
        $('#editClassroomForm').on('submit', function(e) {
            e.preventDefault();

            // Reset previous validation errors
            $('.is-invalid').removeClass('is-invalid');
            $('.invalid-feedback').text('');

            // Show loading state
            const submitBtn = $('#submit-btn');
            const originalBtnText = submitBtn.html();
            submitBtn.html('<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Menyimpan...');
            submitBtn.prop('disabled', true);

            // Submit form via AJAX
            $.ajax({
                url: $(this).attr('action'),
                method: 'POST',
                data: $(this).serialize(),
                dataType: 'json',
                success: function(response) {
                    // Show success notification
                    Swal.fire({
                        title: 'Berhasil!',
                        text: response.message,
                        icon: 'success',
                        showConfirmButton: false,
                        timer: 1500
                    }).then(() => {
                        // Redirect to index
                        window.location.href = "<?php echo e(route('admin.classrooms.index')); ?>";
                    });
                },
                error: function(xhr) {
                    submitBtn.html(originalBtnText);
                    submitBtn.prop('disabled', false);

                    if (xhr.status === 422) {
                        // Validation errors
                        const errors = xhr.responseJSON.errors;
                        $.each(errors, function(field, messages) {
                            const input = $('[name="' + field + '"]');
                            input.addClass('is-invalid');
                            input.siblings('.invalid-feedback').text(messages[0]);
                        });
                    } else {
                        // Other errors
                        Swal.fire({
                            title: 'Error!',
                            text: xhr.responseJSON?.message || 'Terjadi kesalahan saat memperbarui data.',
                            icon: 'error',
                            confirmButtonText: 'OK'
                        });
                    }
                }
            });
        });
    });
</script>
<?php /**PATH C:\laragon\www\rawooh-v2\resources\views/admin/pages/classroom/scripts/edit-form-script.blade.php ENDPATH**/ ?>